﻿using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Events;

namespace AIEvent.Application.Services.Interfaces
{
    public interface IEventService
    {
        Task<BaseResponse<EventResponse>> GetByIdAsync(string id);
        Task<BaseResponse<IEnumerable<EventResponse>>> GetAllAsync();
        Task<BaseResponse<EventResponse>> CreateAsync(CreateEventRequest request);
        Task<BaseResponse<EventResponse>> UpdateAsync(string id, UpdateEventRequest request);
        Task<BaseResponse<object?>> DeleteAsync(string id);

    }
}
