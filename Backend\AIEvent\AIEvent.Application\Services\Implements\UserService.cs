using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.Services.Interfaces;
using AIEvent.Domain.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AIEvent.Application.Services.Implements
{
    public class UserService : IUserService
    {
        private readonly UserManager<AppUser> _userManager;
        private readonly RoleManager<AppRole> _roleManager;
        private readonly ILogger<UserService> _logger;

        public UserService(
            UserManager<AppUser> userManager,
            RoleManager<AppRole> roleManager,
            ILogger<UserService> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task<BaseResponse<UserInfo>> GetUserByIdAsync(string userId)
        {
            try
            {
                if (!Guid.TryParse(userId, out var userGuid))
                {
                    return new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "Invalid user ID format",
                        Errors = ["Invalid user ID"]
                    };
                }

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                var roles = await _userManager.GetRolesAsync(user);
                var userInfo = new UserInfo
                {
                    Id = user.Id.ToString(),
                    Email = user.Email!,
                    FullName = user.FullName ?? "",
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    EmailConfirmed = user.EmailConfirmed,
                    CreatedAt = user.CreatedAt
                };

                return new BaseResponse<UserInfo>
                {
                    Success = true,
                    Message = "User retrieved successfully",
                    Data = userInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
                return new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An error occurred while retrieving user",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<UserInfo>> GetUserByEmailAsync(string email)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                var roles = await _userManager.GetRolesAsync(user);
                var userInfo = new UserInfo
                {
                    Id = user.Id.ToString(),
                    Email = user.Email!,
                    FullName = user.FullName ?? "",
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    EmailConfirmed = user.EmailConfirmed,
                    CreatedAt = user.CreatedAt
                };

                return new BaseResponse<UserInfo>
                {
                    Success = true,
                    Message = "User retrieved successfully",
                    Data = userInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email: {Email}", email);
                return new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An error occurred while retrieving user",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<UserInfo>> UpdateUserAsync(string userId, UpdateUserRequest request)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                // Update user properties
                if (!string.IsNullOrWhiteSpace(request.FullName))
                    user.FullName = request.FullName;

                if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
                    user.PhoneNumber = request.PhoneNumber;

                user.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "Failed to update user",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                var roles = await _userManager.GetRolesAsync(user);
                var userInfo = new UserInfo
                {
                    Id = user.Id.ToString(),
                    Email = user.Email!,
                    FullName = user.FullName ?? "",
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    EmailConfirmed = user.EmailConfirmed,
                    CreatedAt = user.CreatedAt
                };

                return new BaseResponse<UserInfo>
                {
                    Success = true,
                    Message = "User updated successfully",
                    Data = userInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {UserId}", userId);
                return new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An error occurred while updating user",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<bool>> ChangePasswordAsync(string userId, ChangePasswordRequest request)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to change password",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                return new BaseResponse<bool>
                {
                    Success = true,
                    Message = "Password changed successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user: {UserId}", userId);
                return new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An error occurred while changing password",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<bool>> DeactivateUserAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist"]
                    };
                }

                user.IsActive = false;
                user.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to deactivate user",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                return new BaseResponse<bool>
                {
                    Success = true,
                    Message = "User deactivated successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating user: {UserId}", userId);
                return new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An error occurred while deactivating user",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<bool>> ActivateUserAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist"]
                    };
                }

                user.IsActive = true;
                user.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to activate user",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                return new BaseResponse<bool>
                {
                    Success = true,
                    Message = "User activated successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating user: {UserId}", userId);
                return new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An error occurred while activating user",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<List<UserInfo>>> GetAllUsersAsync(int page = 1, int pageSize = 10)
        {
            try
            {
                var users = await _userManager.Users
                    .Where(u => u.IsActive)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var userInfos = new List<UserInfo>();
                foreach (var user in users)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userInfos.Add(new UserInfo
                    {
                        Id = user.Id.ToString(),
                        Email = user.Email!,
                        FullName = user.FullName ?? "",
                        PhoneNumber = user.PhoneNumber,
                        Roles = roles.ToList(),
                        EmailConfirmed = user.EmailConfirmed,
                        CreatedAt = user.CreatedAt
                    });
                }

                return new BaseResponse<List<UserInfo>>
                {
                    Success = true,
                    Message = "Users retrieved successfully",
                    Data = userInfos
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                return new BaseResponse<List<UserInfo>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving users",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<bool>> AssignRoleAsync(string userId, string roleName)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                var roleExists = await _roleManager.RoleExistsAsync(roleName);
                if (!roleExists)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Role not found",
                        Errors = ["Role does not exist"]
                    };
                }

                var result = await _userManager.AddToRoleAsync(user, roleName);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to assign role",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                return new BaseResponse<bool>
                {
                    Success = true,
                    Message = "Role assigned successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning role {Role} to user {UserId}", roleName, userId);
                return new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An error occurred while assigning role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<bool>> RemoveRoleAsync(string userId, string roleName)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                var result = await _userManager.RemoveFromRoleAsync(user, roleName);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to remove role",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                return new BaseResponse<bool>
                {
                    Success = true,
                    Message = "Role removed successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing role {Role} from user {UserId}", roleName, userId);
                return new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An error occurred while removing role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<List<string>>> GetUserRolesAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return new BaseResponse<List<string>>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["User does not exist or is inactive"]
                    };
                }

                var roles = await _userManager.GetRolesAsync(user);
                return new BaseResponse<List<string>>
                {
                    Success = true,
                    Message = "User roles retrieved successfully",
                    Data = roles.ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for user: {UserId}", userId);
                return new BaseResponse<List<string>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving user roles",
                    Errors = [ex.Message]
                };
            }
        }
    }
}
