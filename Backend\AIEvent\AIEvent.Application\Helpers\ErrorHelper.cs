﻿using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Common;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace AIEvent.Application.Helpers
{
    public static class ErrorHelper
    {
        public static BaseResponse<T> CreateValidationErrorResponse<T>(ModelStateDictionary modelState)
        {
            var errors = new List<string>();

            foreach (var state in modelState)
            {
                foreach (var error in state.Value.Errors)
                {
                    errors.Add($"{state.Key}: {error.ErrorMessage}");
                }
            }

            return BaseResponse<T>.FailureResult(errors, MessageResponse.ValidationFailed);
        }

        public static BaseResponse<T> CreateNotFoundResponse<T>(string entityName = "Resource")
        {
            return BaseResponse<T>.NotFoundResult($"{entityName} not found");
        }

        public static BaseResponse<T> CreateExceptionResponse<T>(Exception ex, string operation = "Operation")
        {
            return BaseResponse<T>.FailureResult($"Error during {operation}: {ex.Message}", null, StatusResponse.InternalServerError);
        }

        public static BaseResponse<T> CreateDuplicateResponse<T>(string field)
        {
            return BaseResponse<T>.ConflictResult($"{field} already exists");
        }
    }
}
