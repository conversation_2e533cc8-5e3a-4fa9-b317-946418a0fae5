using AIEvent.Domain.Entity;
using AIEvent.Domain.Identity;
using AIEvent.Domain.Interfaces;
using AIEvent.Infrastructure.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace AIEvent.Infrastructure.Implements
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly DatabaseContext _context;
        private readonly Dictionary<Type, object> _repositories = new Dictionary<Type, object>();
        private IDbContextTransaction? _transaction;
        private IGenericRepository<Event>? _event;
        private IGenericRepository<AppUser> _user;
        private IGenericRepository<AppRole> _role;

        public IGenericRepository<Event> EventRepository =>
            _event ??= new GenericRepository<Event>(_context);

        public IGenericRepository<AppUser> UserRepository =>
            _user ??= new GenericRepository<AppUser>(_context);

        public IGenericRepository<AppRole> RoleRepository =>
            _role ??= new GenericRepository<AppRole>(_context);

        public UnitOfWork(DatabaseContext context)
        {
            _context = context;
        }

        private IGenericRepository<T> GetRepository<T>() where T : class
        {
            if (!_repositories.ContainsKey(typeof(T)))
            {
                _repositories[typeof(T)] = new GenericRepository<T>(_context);
            }
            return (IGenericRepository<T>)_repositories[typeof(T)];
        }

        public GenericRepository<LionAccount> LionAccountRepository()
        {
            return GetRepository<LionAccount>();
        }

        public GenericRepository<LionProfile> LionProfileRepository()
        {
            return GetRepository<LionProfile>();
        }
        public GenericRepository<LionType> LionTypeRepository()
        {
            return GetRepository<LionType>();
        }


        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
