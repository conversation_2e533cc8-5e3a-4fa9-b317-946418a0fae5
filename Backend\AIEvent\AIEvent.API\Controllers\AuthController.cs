using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IJwtService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IJwtService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// User login
        /// </summary>
        /// <param name="request">Login credentials</param>
        /// <returns>Authentication response with tokens</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<BaseResponse<AuthResponse>>> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<AuthResponse>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var result = await _authService.LoginAsync(request);
                
                if (!result.Success)
                {
                    return BadRequest(new BaseResponse<AuthResponse>
                    {
                        Success = false,
                        Message = result.Message,
                        Errors = result.Errors
                    });
                }

                return Ok(new BaseResponse<AuthResponse>
                {
                    Success = true,
                    Message = result.Message,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during login for user: {Email}", request.Email);
                return StatusCode(500, new BaseResponse<AuthResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// User registration
        /// </summary>
        /// <param name="request">Registration data</param>
        /// <returns>Authentication response with tokens</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<ActionResult<BaseResponse<AuthResponse>>> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<AuthResponse>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var result = await _authService.RegisterAsync(request);
                
                if (!result.Success)
                {
                    return BadRequest(new BaseResponse<AuthResponse>
                    {
                        Success = false,
                        Message = result.Message,
                        Errors = result.Errors
                    });
                }

                return Ok(new BaseResponse<AuthResponse>
                {
                    Success = true,
                    Message = result.Message,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during registration for user: {Email}", request.Email);
                return StatusCode(500, new BaseResponse<AuthResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Refresh access token using refresh token
        /// </summary>
        /// <param name="request">Refresh token request</param>
        /// <returns>New authentication response with tokens</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<ActionResult<BaseResponse<AuthResponse>>> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<AuthResponse>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var result = await _authService.RefreshTokenAsync(request.RefreshToken);
                
                if (!result.Success)
                {
                    return BadRequest(new BaseResponse<AuthResponse>
                    {
                        Success = false,
                        Message = result.Message,
                        Errors = result.Errors
                    });
                }

                return Ok(new BaseResponse<AuthResponse>
                {
                    Success = true,
                    Message = result.Message,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during token refresh");
                return StatusCode(500, new BaseResponse<AuthResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Revoke refresh token (logout)
        /// </summary>
        /// <param name="request">Refresh token to revoke</param>
        /// <returns>Success response</returns>
        [HttpPost("revoke-token")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<object>>> RevokeToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<object>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                await _authService.RevokeRefreshTokenAsync(request.RefreshToken);

                return Ok(new BaseResponse<object>
                {
                    Success = true,
                    Message = "Token revoked successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during token revocation");
                return StatusCode(500, new BaseResponse<object>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get current user information
        /// </summary>
        /// <returns>Current user info</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<UserInfo>>> GetCurrentUser()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["Invalid user token"]
                    });
                }

                var userInfo = new UserInfo
                {
                    Id = userId,
                    Email = User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value ?? "",
                    FullName = User.FindFirst("FullName")?.Value ?? "",
                    Roles = User.FindAll(System.Security.Claims.ClaimTypes.Role).Select(c => c.Value).ToList()
                };

                return Ok(new BaseResponse<UserInfo>
                {
                    Success = true,
                    Message = "User information retrieved successfully",
                    Data = userInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting current user info");
                return StatusCode(500, new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }
    }
}
