using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;

namespace AIEvent.Application.Services.Interfaces
{
    public interface IUserService
    {
        Task<BaseResponse<UserInfo>> GetUserByIdAsync(string userId);
        Task<BaseResponse<UserInfo>> GetUserByEmailAsync(string email);
        Task<BaseResponse<UserInfo>> UpdateUserAsync(string userId, UpdateUserRequest request);
        Task<BaseResponse<bool>> ChangePasswordAsync(string userId, ChangePasswordRequest request);
        Task<BaseResponse<bool>> DeactivateUserAsync(string userId);
        Task<BaseResponse<bool>> ActivateUserAsync(string userId);
        Task<BaseResponse<List<UserInfo>>> GetAllUsersAsync(int page = 1, int pageSize = 10);
        Task<BaseResponse<bool>> AssignRoleAsync(string userId, string roleName);
        Task<BaseResponse<bool>> RemoveRoleAsync(string userId, string roleName);
        Task<BaseResponse<List<string>>> GetUserRolesAsync(string userId);
    }
}
