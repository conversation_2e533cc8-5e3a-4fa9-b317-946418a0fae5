﻿namespace AIEvent.Application.Constants
{
    public class MessageResponse
    {
        public const string Success = "Operation completed successfully";
        public const string Created = "Resource created successfully";
        public const string Updated = "Resource updated successfully";
        public const string Deleted = "Resource deleted successfully";
        public const string Retrieved = "Resource retrieved successfully";

        public const string Error = "An error occurred during the operation";
        public const string NotFound = "The requested resource was not found";
        public const string BadRequest = "The request was invalid or cannot be served";
        public const string Unauthorized = "You are not authorized to perform this action";
        public const string Forbidden = "Access to this resource is forbidden";
        public const string Conflict = "The request conflicts with the current state of the resource";
        public const string ValidationFailed = "Validation failed for the provided data";
        public const string InternalServerError = "An internal server error occurred";

    }
}
