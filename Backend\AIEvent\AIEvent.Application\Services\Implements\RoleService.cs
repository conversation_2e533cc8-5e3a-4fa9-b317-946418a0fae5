using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Role;
using AIEvent.Application.Services.Interfaces;
using AIEvent.Domain.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AIEvent.Application.Services.Implements
{
    public class RoleService : IRoleService
    {
        private readonly RoleManager<AppRole> _roleManager;
        private readonly UserManager<AppUser> _userManager;
        private readonly ILogger<RoleService> _logger;

        public RoleService(
            RoleManager<AppRole> roleManager,
            UserManager<AppUser> userManager,
            ILogger<RoleService> logger)
        {
            _roleManager = roleManager;
            _userManager = userManager;
            _logger = logger;
        }

        public async Task<BaseResponse<List<RoleInfo>>> GetAllRolesAsync()
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                var roleInfos = roles.Select(r => new RoleInfo
                {
                    Id = r.Id.ToString(),
                    Name = r.Name!,
                    Description = r.Description,
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt
                }).ToList();

                return new BaseResponse<List<RoleInfo>>
                {
                    Success = true,
                    Message = "Roles retrieved successfully",
                    Data = roleInfos
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all roles");
                return new BaseResponse<List<RoleInfo>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving roles",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<RoleInfo>> GetRoleByIdAsync(string roleId)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return new BaseResponse<RoleInfo>
                    {
                        Success = false,
                        Message = "Role not found",
                        Errors = ["Role does not exist"]
                    };
                }

                var roleInfo = new RoleInfo
                {
                    Id = role.Id.ToString(),
                    Name = role.Name!,
                    Description = role.Description,
                    CreatedAt = role.CreatedAt,
                    UpdatedAt = role.UpdatedAt
                };

                return new BaseResponse<RoleInfo>
                {
                    Success = true,
                    Message = "Role retrieved successfully",
                    Data = roleInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role by ID: {RoleId}", roleId);
                return new BaseResponse<RoleInfo>
                {
                    Success = false,
                    Message = "An error occurred while retrieving role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<RoleInfo>> GetRoleByNameAsync(string roleName)
        {
            try
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role == null)
                {
                    return new BaseResponse<RoleInfo>
                    {
                        Success = false,
                        Message = "Role not found",
                        Errors = ["Role does not exist"]
                    };
                }

                var roleInfo = new RoleInfo
                {
                    Id = role.Id.ToString(),
                    Name = role.Name!,
                    Description = role.Description,
                    CreatedAt = role.CreatedAt,
                    UpdatedAt = role.UpdatedAt
                };

                return new BaseResponse<RoleInfo>
                {
                    Success = true,
                    Message = "Role retrieved successfully",
                    Data = roleInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role by name: {RoleName}", roleName);
                return new BaseResponse<RoleInfo>
                {
                    Success = false,
                    Message = "An error occurred while retrieving role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<RoleInfo>> CreateRoleAsync(CreateRoleRequest request)
        {
            try
            {
                var existingRole = await _roleManager.FindByNameAsync(request.Name);
                if (existingRole != null)
                {
                    return new BaseResponse<RoleInfo>
                    {
                        Success = false,
                        Message = "Role already exists",
                        Errors = ["A role with this name already exists"]
                    };
                }

                var role = new AppRole
                {
                    Name = request.Name,
                    Description = request.Description,
                    CreatedAt = DateTime.UtcNow
                };

                var result = await _roleManager.CreateAsync(role);
                if (!result.Succeeded)
                {
                    return new BaseResponse<RoleInfo>
                    {
                        Success = false,
                        Message = "Failed to create role",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                var roleInfo = new RoleInfo
                {
                    Id = role.Id.ToString(),
                    Name = role.Name!,
                    Description = role.Description,
                    CreatedAt = role.CreatedAt,
                    UpdatedAt = role.UpdatedAt
                };

                return new BaseResponse<RoleInfo>
                {
                    Success = true,
                    Message = "Role created successfully",
                    Data = roleInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating role: {RoleName}", request.Name);
                return new BaseResponse<RoleInfo>
                {
                    Success = false,
                    Message = "An error occurred while creating role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<RoleInfo>> UpdateRoleAsync(string roleId, UpdateRoleRequest request)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return new BaseResponse<RoleInfo>
                    {
                        Success = false,
                        Message = "Role not found",
                        Errors = ["Role does not exist"]
                    };
                }

                role.Description = request.Description;
                role.UpdatedAt = DateTime.UtcNow;

                var result = await _roleManager.UpdateAsync(role);
                if (!result.Succeeded)
                {
                    return new BaseResponse<RoleInfo>
                    {
                        Success = false,
                        Message = "Failed to update role",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                var roleInfo = new RoleInfo
                {
                    Id = role.Id.ToString(),
                    Name = role.Name!,
                    Description = role.Description,
                    CreatedAt = role.CreatedAt,
                    UpdatedAt = role.UpdatedAt
                };

                return new BaseResponse<RoleInfo>
                {
                    Success = true,
                    Message = "Role updated successfully",
                    Data = roleInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating role: {RoleId}", roleId);
                return new BaseResponse<RoleInfo>
                {
                    Success = false,
                    Message = "An error occurred while updating role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<bool>> DeleteRoleAsync(string roleId)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Role not found",
                        Errors = ["Role does not exist"]
                    };
                }

                // Check if any users are assigned to this role
                var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name!);
                if (usersInRole.Any())
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Cannot delete role",
                        Errors = ["Role is assigned to one or more users"]
                    };
                }

                var result = await _roleManager.DeleteAsync(role);
                if (!result.Succeeded)
                {
                    return new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to delete role",
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                return new BaseResponse<bool>
                {
                    Success = true,
                    Message = "Role deleted successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting role: {RoleId}", roleId);
                return new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An error occurred while deleting role",
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<BaseResponse<List<string>>> GetUsersInRoleAsync(string roleName)
        {
            try
            {
                var roleExists = await _roleManager.RoleExistsAsync(roleName);
                if (!roleExists)
                {
                    return new BaseResponse<List<string>>
                    {
                        Success = false,
                        Message = "Role not found",
                        Errors = ["Role does not exist"]
                    };
                }

                var users = await _userManager.GetUsersInRoleAsync(roleName);
                var userEmails = users.Select(u => u.Email!).ToList();

                return new BaseResponse<List<string>>
                {
                    Success = true,
                    Message = "Users in role retrieved successfully",
                    Data = userEmails
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users in role: {RoleName}", roleName);
                return new BaseResponse<List<string>>
                {
                    Success = false,
                    Message = "An error occurred while retrieving users in role",
                    Errors = [ex.Message]
                };
            }
        }
    }
}
