using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Role;

namespace AIEvent.Application.Services.Interfaces
{
    public interface IRoleService
    {
        Task<BaseResponse<List<RoleInfo>>> GetAllRolesAsync();
        Task<BaseResponse<RoleInfo>> GetRoleByIdAsync(string roleId);
        Task<BaseResponse<RoleInfo>> GetRoleByNameAsync(string roleName);
        Task<BaseResponse<RoleInfo>> CreateRoleAsync(CreateRoleRequest request);
        Task<BaseResponse<RoleInfo>> UpdateRoleAsync(string roleId, UpdateRoleRequest request);
        Task<BaseResponse<bool>> DeleteRoleAsync(string roleId);
        Task<BaseResponse<List<string>>> GetUsersInRoleAsync(string roleName);
    }
}
