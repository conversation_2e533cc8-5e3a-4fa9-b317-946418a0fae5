﻿using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Events;
using AIEvent.Application.Helpers;
using AIEvent.Application.Services.Interfaces;
using AIEvent.Domain.Entity;
using AIEvent.Domain.Interfaces;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace AIEvent.Application.Services.Implements
{
    public class EventService : IEventService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        public EventService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }
        public async Task<BaseResponse<EventResponse>> CreateAsync(CreateEventRequest request)
        {
            try
            {
                var ev = _mapper.Map<Event>(request);

                await _unitOfWork.EventRepository.AddAsync(ev);
                await _unitOfWork.SaveChangesAsync();

                var eventResponse = _mapper.Map<EventResponse>(ev);
                return BaseResponse<EventResponse>.SuccessResult(eventResponse, MessageResponse.Created);
            }
            catch (Exception ex)
            {
                return ErrorHelper.CreateExceptionResponse<EventResponse>(ex, "creating event");
            }
        }

        public async Task<BaseResponse<object?>> DeleteAsync(string id)
        {
            try
            {
                var ev = await _unitOfWork.EventRepository.GetByIdAsync(id);
                if (ev == null || ev.IsDeleted)
                {
                    return ErrorHelper.CreateNotFoundResponse<object?>("Event");
                }

                await _unitOfWork.EventRepository.DeleteAsync(ev);
                await _unitOfWork.SaveChangesAsync();

                return BaseResponse<object?>.SuccessResult(null, MessageResponse.Deleted);
            }
            catch (Exception ex)
            {
                return ErrorHelper.CreateExceptionResponse<object?>(ex, "deleting event");
            }
        }

        public async Task<BaseResponse<IEnumerable<EventResponse>>> GetAllAsync()
        {
            try
            {
                var ev = await _unitOfWork.EventRepository.GetAllAsync();

                var eventResponses = _mapper.Map<IEnumerable<EventResponse>>(ev);

                return BaseResponse<IEnumerable<EventResponse>>.SuccessResult(eventResponses, MessageResponse.Retrieved);
            }
            catch (Exception ex)
            {
                return ErrorHelper.CreateExceptionResponse<IEnumerable<EventResponse>>(ex, "retrieving event");
            }
        }

        public async Task<BaseResponse<EventResponse>> GetByIdAsync(string id)
        {
            try
            {
                var ev = await _unitOfWork.EventRepository.Query(true)
                    .FirstOrDefaultAsync(e => e.Id == id && !e.IsDeleted);

                if (ev == null)
                {
                    return ErrorHelper.CreateNotFoundResponse<EventResponse>("Event");
                }

                var eventResponse = _mapper.Map<EventResponse>(ev);
                return BaseResponse<EventResponse>.SuccessResult(eventResponse, MessageResponse.Retrieved);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<BaseResponse<EventResponse>> UpdateAsync(string eventId, UpdateEventRequest request)
        {
            try
            {
                var existingEvent = await _unitOfWork.EventRepository.GetByIdAsync(eventId);
                if (existingEvent == null || existingEvent.IsDeleted)
                {
                    return ErrorHelper.CreateNotFoundResponse<EventResponse>("Event");
                }

                _mapper.Map(request, existingEvent);

                await _unitOfWork.EventRepository.UpdateAsync(existingEvent);
                await _unitOfWork.SaveChangesAsync();

                var eventResponse = _mapper.Map<EventResponse>(existingEvent);
                return BaseResponse<EventResponse>.SuccessResult(eventResponse, MessageResponse.Updated);
            }
            catch (Exception ex)
            {
                return ErrorHelper.CreateExceptionResponse<EventResponse>(ex, "updating event");
            }
        }
    }
}
