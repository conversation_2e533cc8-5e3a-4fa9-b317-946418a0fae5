﻿using AIEvent.Application.Constants;

namespace AIEvent.Application.DTO.Common
{
    public class BaseResponse<T>
    {
        public bool Success { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public List<string> Errors { get; set; } = [];

        public static BaseResponse<T> SuccessResult(T data, string message = MessageResponse.Success)
        {
            return new BaseResponse<T>
            {
                Success = true,
                Status = StatusResponse.Success,
                Message = message,
                Data = data
            };
        }

        public static BaseResponse<T> FailureResult(string message, List<string>? errors = null, string status = StatusResponse.Error)
        {
            return new BaseResponse<T>
            {
                Success = false,
                Status = status,
                Message = message,
                Errors = errors ?? []
            };
        }

        public static BaseResponse<T> FailureResult(List<string> errors, string message = MessageResponse.ValidationFailed)
        {
            return new BaseResponse<T>
            {
                Success = false,
                Status = StatusResponse.ValidationFailed,
                Message = message,
                Errors = errors
            };
        }

        public static BaseResponse<T> NotFoundResult(string message = MessageResponse.NotFound)
        {
            return new BaseResponse<T>
            {
                Success = false,
                Status = StatusResponse.NotFound,
                Message = message
            };
        }

        public static BaseResponse<T> BadRequestResult(string message = MessageResponse.BadRequest)
        {
            return new BaseResponse<T>
            {
                Success = false,
                Status = StatusResponse.BadRequest,
                Message = message
            };
        }

        public static BaseResponse<T> ConflictResult(string message = MessageResponse.Conflict)
        {
            return new BaseResponse<T>
            {
                Success = false,
                Status = StatusResponse.Conflict,
                Message = message
            };
        }

        public static BaseResponse<T> UnauthorizedResult(string message = MessageResponse.Unauthorized)
        {
            return new BaseResponse<T>
            {
                Success = false,
                Status = StatusResponse.Unauthorized,
                Message = message
            };
        }
    }
}
