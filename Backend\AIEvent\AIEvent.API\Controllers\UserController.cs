using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User information</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<UserInfo>>> GetUser(string id)
        {
            try
            {
                var result = await _userService.GetUserByIdAsync(id);
                
                if (!result.Success)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user: {UserId}", id);
                return StatusCode(500, new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get current user profile
        /// </summary>
        /// <returns>Current user information</returns>
        [HttpGet("profile")]
        public async Task<ActionResult<BaseResponse<UserInfo>>> GetProfile()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["Invalid user token"]
                    });
                }

                var result = await _userService.GetUserByIdAsync(userId);
                
                if (!result.Success)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user profile");
                return StatusCode(500, new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Update current user profile
        /// </summary>
        /// <param name="request">Update user request</param>
        /// <returns>Updated user information</returns>
        [HttpPut("profile")]
        public async Task<ActionResult<BaseResponse<UserInfo>>> UpdateProfile([FromBody] UpdateUserRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new BaseResponse<UserInfo>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["Invalid user token"]
                    });
                }

                var result = await _userService.UpdateUserAsync(userId, request);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user profile");
                return StatusCode(500, new BaseResponse<UserInfo>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Change current user password
        /// </summary>
        /// <param name="request">Change password request</param>
        /// <returns>Success response</returns>
        [HttpPost("change-password")]
        public async Task<ActionResult<BaseResponse<bool>>> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new BaseResponse<bool>
                    {
                        Success = false,
                        Message = "User not found",
                        Errors = ["Invalid user token"]
                    });
                }

                var result = await _userService.ChangePasswordAsync(userId, request);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password");
                return StatusCode(500, new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get all users (Admin only)
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>List of users</returns>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<List<UserInfo>>>> GetAllUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var result = await _userService.GetAllUsersAsync(page, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                return StatusCode(500, new BaseResponse<List<UserInfo>>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Assign role to user (Admin only)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="roleName">Role name</param>
        /// <returns>Success response</returns>
        [HttpPost("{userId}/roles/{roleName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<bool>>> AssignRole(string userId, string roleName)
        {
            try
            {
                var result = await _userService.AssignRoleAsync(userId, roleName);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning role {Role} to user {UserId}", roleName, userId);
                return StatusCode(500, new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Remove role from user (Admin only)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="roleName">Role name</param>
        /// <returns>Success response</returns>
        [HttpDelete("{userId}/roles/{roleName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<bool>>> RemoveRole(string userId, string roleName)
        {
            try
            {
                var result = await _userService.RemoveRoleAsync(userId, roleName);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing role {Role} from user {UserId}", roleName, userId);
                return StatusCode(500, new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get user roles
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of user roles</returns>
        [HttpGet("{userId}/roles")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<List<string>>>> GetUserRoles(string userId)
        {
            try
            {
                var result = await _userService.GetUserRolesAsync(userId);
                
                if (!result.Success)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
                return StatusCode(500, new BaseResponse<List<string>>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Deactivate user (Admin only)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success response</returns>
        [HttpPost("{userId}/deactivate")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<bool>>> DeactivateUser(string userId)
        {
            try
            {
                var result = await _userService.DeactivateUserAsync(userId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating user {UserId}", userId);
                return StatusCode(500, new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Activate user (Admin only)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success response</returns>
        [HttpPost("{userId}/activate")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<BaseResponse<bool>>> ActivateUser(string userId)
        {
            try
            {
                var result = await _userService.ActivateUserAsync(userId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating user {UserId}", userId);
                return StatusCode(500, new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }
    }
}
